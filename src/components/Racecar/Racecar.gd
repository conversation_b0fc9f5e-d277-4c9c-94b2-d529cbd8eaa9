extends CharacterBody2D

class_name Racecar

@export var start_position: Marker2D
var car_heading: float = 0.0
var steer_angle: float = 0.0

var acceleration: Vector2 = Vector2.ZERO

var engine_power: float = 2.0
var brake_power: float = 5.0
var reverse_power: float = 1.0

var is_reversing: bool = false

var track_friction: float = -0.002  # -0.0005
var off_road_friction: float = -0.01
var slowed_friction: float = -0.1
var friction: float = 0.0
var drag: float = -0.00001

var slip_speed: float = 150.0
var traction_when_slipping: float = 0.0001
var traction_when_gripping: float = 1.0

var stall_time: float = 0.5
var is_stalled: bool = false
var stall_countdown: float = 0.0
var is_crashed: bool = false

var front_wheel: Marker2D
var back_wheel: Marker2D
var car_sprite: Sprite2D
var engine_sound: AudioStreamPlayer2D
var tire_screech: AudioStreamPlayer2D
var animation_player: AnimationPlayer
var traction_area: Interaction2d

enum AnimateTurn {
	NONE = 0,
	LEFT = 1,
	RIGHT = 2,
}

func _ready():
	front_wheel = get_node("Front")
	back_wheel = get_node("Back")
	car_sprite = get_node("Sprite2D")
	engine_sound = get_node("EngineSound")
	tire_screech = get_node("TireScreech")
	animation_player = get_node("AnimationPlayer")
	traction_area = get_node("TractionArea")
	
	reset()

func _physics_process(delta):
	if stall_countdown > 0:
		stall_countdown -= delta
		if stall_countdown <= 0:
			is_stalled = false
			if is_crashed:
				reset()
		
		if is_stalled:
			move_and_slide()
			return
	
	var turn = 0
	if Input.is_action_pressed("steer_right"):
		turn = 1
		car_sprite.set_frame(AnimateTurn.RIGHT)
	if Input.is_action_pressed("steer_left"):
		turn = -1
		car_sprite.set_frame(AnimateTurn.LEFT)
	
	if turn == 0:
		car_sprite.set_frame(AnimateTurn.NONE)
	
	if Input.is_action_pressed("brake-reverse"):
		engine_sound.stop()
		if is_reversing:
			acceleration = -1 * transform.x * reverse_power
		else:
			acceleration = -1 * transform.x * brake_power
	else:
		acceleration = transform.x * engine_power
		if not engine_sound.is_playing():
			engine_sound.play()
	
	if traction_area.interactions().size() > 0:
		friction = track_friction
		for interaction in traction_area.interactions():
			if interaction.name == "SlowArea":
				friction = slowed_friction
				break
	else:
		friction = off_road_friction
	
	var friction_force = velocity * friction
	var drag_force = velocity * velocity.length() * drag
	
	acceleration += friction_force + drag_force
	
	var traction = traction_when_gripping
	var turn_radians = turn * deg_to_rad(0.2)
	if velocity.length() < slip_speed:
		turn_radians = turn * deg_to_rad(0.75)
	if turn != 0 and Input.is_action_pressed("drift"):
		traction = traction_when_slipping
		turn_radians = turn * deg_to_rad(0.5)
		acceleration = Vector2.ZERO
		if not tire_screech.is_playing():
			tire_screech.play()
	steer_angle = turn_radians
	
	var back_wheel_pos = back_wheel.global_position + velocity
	var front_wheel_pos = front_wheel.global_position + velocity.rotated(steer_angle)
	var new_heading = back_wheel_pos.direction_to(front_wheel_pos)
	
	var new_heading_dot = new_heading.dot(velocity.normalized())
	if new_heading_dot > 0:
		velocity = velocity.lerp(new_heading * velocity.length(), traction)
		is_reversing = false
	else:
		velocity = velocity.lerp(-1 * new_heading * velocity.length(), traction)
		is_reversing = true
	
	velocity += acceleration
	
	rotation = new_heading.angle()
	
	var collision = move_and_collide(velocity * delta)
	
	if collision != null:
		var collider = collision.get_collider()
		if collider is StaticBody2D:
			is_stalled = true
			stall_countdown = stall_time
			if collider.name == "CrashObstacles":
				print("Crash!")
				is_crashed = true
			elif collider.name == "BounceObstacles":
				velocity = collision.get_normal() * 50.0
	
	if velocity.length() < 10:
		animation_player.play("idle")
	elif velocity.length() < slip_speed / 2:
		animation_player.play("slow")
	elif velocity.length() < slip_speed:
		animation_player.play("medium")
	else:
		animation_player.play("fast")

func reset():
	global_position = start_position.global_position
	rotation = start_position.rotation
	velocity = Vector2.ZERO
	acceleration = Vector2.ZERO
	steer_angle = 0
	is_crashed = false
	is_stalled = false
	stall_countdown = 0
