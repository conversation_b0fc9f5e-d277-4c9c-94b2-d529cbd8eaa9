[gd_scene load_steps=56 format=3 uid="uid://7o8wv0nvc7p7"]

[ext_resource type="Texture2D" uid="uid://copqead8ly04i" path="res://assets/sprites/racetrack-final.png" id="1_sc3sn"]
[ext_resource type="Script" uid="uid://cr6j8cxjts3oh" path="res://src/components/Racecar/Racecar.gd" id="2_mta1t"]
[ext_resource type="Texture2D" uid="uid://btwoppdslv6gb" path="res://assets/sprites/rainbow-car-2.png" id="3_0u4bd"]
[ext_resource type="AudioStream" uid="uid://d0el54acetivx" path="res://assets/audio/RaceCarEngine.wav" id="5_7sjdd"]
[ext_resource type="AudioStream" uid="uid://cqvfm5163efe0" path="res://assets/audio/RaceCarEnginePitchedDown.wav" id="5_jc4sb"]
[ext_resource type="AudioStream" uid="uid://6qcm6o7yxhbh" path="res://assets/audio/RaceCarEnginePitchedUp.wav" id="6_0u4bd"]
[ext_resource type="Script" uid="uid://bd34xhus3sci7" path="res://src/components/MiniMap/MiniMap.cs" id="6_jy1fd"]
[ext_resource type="AudioStream" uid="uid://canwhgoge05b0" path="res://assets/audio/RaceCarTireScreech.wav" id="8_0u4bd"]
[ext_resource type="Texture2D" uid="uid://b71uqr6ocs8qa" path="res://assets/sprites/small-smoke.png" id="9_erbwi"]
[ext_resource type="Texture2D" uid="uid://cw5gfspcmeium" path="res://assets/sprites/medium-smoke.png" id="10_s07sl"]
[ext_resource type="Texture2D" uid="uid://bqt7dk1lnwtep" path="res://assets/sprites/large-smoke.png" id="11_sc3sn"]
[ext_resource type="Script" uid="uid://bn2cn72lupgrn" path="res://src/components/LapTimer/LapTimer.gd" id="12_mta1t"]
[ext_resource type="Script" uid="uid://dffdybbbesnrc" path="res://src/components/Interaction2D/Interaction2d.gd" id="14_mta1t"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_dql50"]
radius = 12.0
height = 40.0

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_0u4bd"]
radius = 5.96244
height = 40.0

[sub_resource type="AudioStreamPlaylist" id="AudioStreamPlaylist_erbwi"]
shuffle = true
stream_count = 3
stream_0 = ExtResource("5_jc4sb")
stream_1 = ExtResource("6_0u4bd")
stream_2 = ExtResource("5_7sjdd")

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_4lbuf"]
radius = 12.0
height = 40.0

[sub_resource type="Animation" id="Animation_mta1t"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SmallSmoke:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("MediumSmoke:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("LargeSmoke:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("SmallSmoke:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-25, 5)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("MediumSmoke:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-25, 5)]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("LargeSmoke:position")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-26, 5)]
}

[sub_resource type="Animation" id="Animation_wiju8"]
resource_name = "fast"
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SmallSmoke:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0.01, 0.176667, 0.376667, 0.576667, 0.776667),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 1,
"values": [true, false, true, false, true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("MediumSmoke:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.133333, 0.333333, 0.533333, 0.733333, 0.933333, 1.03333),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [false, true, false, true, false, true, false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("LargeSmoke:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0.01, 0.243333, 0.443333, 0.643333, 0.843333, 1.04333),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [true, false, true, false, true, false]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("SmallSmoke:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-36, 4)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("MediumSmoke:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-32, 4)]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("LargeSmoke:position")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-26, 5)]
}

[sub_resource type="Animation" id="Animation_sc3sn"]
resource_name = "idle"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SmallSmoke:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("MediumSmoke:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("LargeSmoke:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_ngti7"]
resource_name = "medium"
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SmallSmoke:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.133333, 0.333333, 0.533333, 0.733333, 0.933333, 1.03333),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [false, true, false, true, false, true, false]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("MediumSmoke:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4, 0.6, 0.8, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [true, false, true, false, true, false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("LargeSmoke:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("MediumSmoke:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-25, 5)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("SmallSmoke:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-30, 4)]
}

[sub_resource type="Animation" id="Animation_cjelv"]
resource_name = "slow"
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SmallSmoke:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4, 0.6, 0.8, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [true, false, true, false, true, false]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("MediumSmoke:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("LargeSmoke:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("SmallSmoke:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-25, 5)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_cjelv"]
_data = {
&"RESET": SubResource("Animation_mta1t"),
&"fast": SubResource("Animation_wiju8"),
&"idle": SubResource("Animation_sc3sn"),
&"medium": SubResource("Animation_ngti7"),
&"slow": SubResource("Animation_cjelv")
}

[sub_resource type="CircleShape2D" id="CircleShape2D_4lbuf"]
radius = 12.0

[sub_resource type="CircleShape2D" id="CircleShape2D_jy1fd"]
radius = 13.0384

[sub_resource type="CircleShape2D" id="CircleShape2D_7sjdd"]
radius = 7.0

[sub_resource type="CircleShape2D" id="CircleShape2D_rtth0"]
radius = 19.0

[sub_resource type="CircleShape2D" id="CircleShape2D_jc4sb"]
radius = 11.0454

[sub_resource type="CircleShape2D" id="CircleShape2D_0u4bd"]
radius = 18.0278

[sub_resource type="CircleShape2D" id="CircleShape2D_erbwi"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_0u4bd"]
size = Vector2(143, 34)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_erbwi"]
size = Vector2(286, 139)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_s07sl"]
size = Vector2(139, 285)

[sub_resource type="CircleShape2D" id="CircleShape2D_5xlva"]

[sub_resource type="CircleShape2D" id="CircleShape2D_tdodm"]
radius = 5.0

[sub_resource type="CircleShape2D" id="CircleShape2D_kl3yp"]
radius = 17.0294

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_scayd"]
radius = 6.0
height = 46.0

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_erbwi"]
radius = 8.0
height = 1072.0

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_s07sl"]
radius = 33.0
height = 160.0

[sub_resource type="CircleShape2D" id="CircleShape2D_s07sl"]
radius = 8.0

[sub_resource type="CircleShape2D" id="CircleShape2D_sc3sn"]
radius = 11.0

[sub_resource type="CircleShape2D" id="CircleShape2D_mta1t"]
radius = 8.0

[sub_resource type="CircleShape2D" id="CircleShape2D_cjelv"]

[sub_resource type="CircleShape2D" id="CircleShape2D_ngti7"]
radius = 13.0

[sub_resource type="CircleShape2D" id="CircleShape2D_wiju8"]
radius = 8.06226

[sub_resource type="RectangleShape2D" id="RectangleShape2D_4lbuf"]

[sub_resource type="LabelSettings" id="LabelSettings_0u4bd"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_sc3sn"]
size = Vector2(135, 450.5)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_mta1t"]
size = Vector2(1022, 11)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_cjelv"]
size = Vector2(12, 989)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_ngti7"]
size = Vector2(183, 79)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_wiju8"]
size = Vector2(304, 11)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_5xlva"]
size = Vector2(462, 11)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_tdodm"]
size = Vector2(1175, 10)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_kl3yp"]
size = Vector2(1306, 10)

[node name="Demo Track" type="Node2D"]

[node name="Track-zero-sheet" type="Sprite2D" parent="."]
position = Vector2(0, -3)
texture = ExtResource("1_sc3sn")

[node name="Racecar" type="CharacterBody2D" parent="." node_paths=PackedStringArray("start_position")]
script = ExtResource("2_mta1t")
start_position = NodePath("../StartPosition")

[node name="Sprite2D" type="Sprite2D" parent="Racecar"]
rotation = 1.5708
texture = ExtResource("3_0u4bd")
hframes = 3

[node name="Camera2D" type="Camera2D" parent="Racecar"]
zoom = Vector2(1.25, 1.25)
limit_left = -1000
limit_top = -1500
limit_right = 1000
limit_bottom = 1500
drag_left_margin = 0.4
drag_top_margin = 0.4
drag_right_margin = 0.4
drag_bottom_margin = 0.4

[node name="CollisionShape2D" type="CollisionShape2D" parent="Racecar"]
rotation = 1.5708
shape = SubResource("CapsuleShape2D_dql50")

[node name="Front" type="Marker2D" parent="Racecar"]
position = Vector2(20, 0)

[node name="Back" type="Marker2D" parent="Racecar"]
position = Vector2(-10, 0)

[node name="TriggerArea" type="Area2D" parent="Racecar"]
scale = Vector2(1, 1.0063)
collision_layer = 2
collision_mask = 2
script = ExtResource("14_mta1t")

[node name="CollisionShape2D2" type="CollisionShape2D" parent="Racecar/TriggerArea"]
rotation = 1.5708
shape = SubResource("CapsuleShape2D_0u4bd")

[node name="EngineSound" type="AudioStreamPlayer2D" parent="Racecar"]
stream = SubResource("AudioStreamPlaylist_erbwi")

[node name="TireScreech" type="AudioStreamPlayer2D" parent="Racecar"]
stream = ExtResource("8_0u4bd")

[node name="TractionArea" type="Area2D" parent="Racecar"]
collision_layer = 4
collision_mask = 4
script = ExtResource("14_mta1t")

[node name="CollisionShape2D3" type="CollisionShape2D" parent="Racecar/TractionArea"]
rotation = 1.5708
scale = Vector2(1.0063, 1)
shape = SubResource("CapsuleShape2D_4lbuf")

[node name="SmallSmoke" type="Sprite2D" parent="Racecar"]
visible = false
position = Vector2(-25, 5)
texture = ExtResource("9_erbwi")

[node name="MediumSmoke" type="Sprite2D" parent="Racecar"]
visible = false
position = Vector2(-25, 5)
texture = ExtResource("10_s07sl")

[node name="LargeSmoke" type="Sprite2D" parent="Racecar"]
visible = false
position = Vector2(-26, 5)
texture = ExtResource("11_sc3sn")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Racecar"]
libraries = {
&"": SubResource("AnimationLibrary_cjelv")
}

[node name="CrashObstacles" type="StaticBody2D" parent="."]

[node name="CollisionShape2D3" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-296, -696)
shape = SubResource("CircleShape2D_4lbuf")

[node name="CollisionShape2D4" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(278, -717)
shape = SubResource("CircleShape2D_jy1fd")

[node name="CollisionShape2D5" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(282, -686)
shape = SubResource("CircleShape2D_7sjdd")

[node name="CollisionShape2D6" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(643, -1134)
shape = SubResource("CircleShape2D_rtth0")

[node name="CollisionShape2D7" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(66, -1175)
shape = SubResource("CircleShape2D_jc4sb")

[node name="CollisionShape2D8" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-548, -1347)
shape = SubResource("CircleShape2D_0u4bd")

[node name="CollisionShape2D" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(711, 1200)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D2" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(742, 1167)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D9" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(714, 1104)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D10" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(875, 1148)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D11" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(913, 1207)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D21" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(892, 1297)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D22" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(911, 1386)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D23" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(845, 1407)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D24" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(780, 1460)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D25" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(669, 945)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D12" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(903, 1110)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D13" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(874, 1074)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D14" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(744, 1074)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D15" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(717, 1014)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionShape2D16" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(907, 1020)
shape = SubResource("CircleShape2D_erbwi")

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="CrashObstacles"]
polygon = PackedVector2Array(583, 488, 645, 561, 671, 528, 627, 491, 609, 391, 626, 359, 634, 336, 622, 298, 676, 291, 681, 275, 681, 242, 742, 226, 701, 193, 722, 183, 746, 133, 714, 110, 701, 85, 623, -16, 620, -59, 606, -63, 573, -25, 609, -10, 644, 22, 630, 52, 666, 56, 682, 87, 672, 93, 671, 117, 651, 139, 672, 166, 676, 187, 672, 227, 663, 266, 612, 285, 613, 337, 574, 325, 565, 341, 613, 353, 575, 395)

[node name="CollisionPolygon2D2" type="CollisionPolygon2D" parent="CrashObstacles"]
polygon = PackedVector2Array(90, 383, -69, 248, 13, 244, 69, 215, 69, 188, 119, 180, 129, 119, 173, 84, 235, 88, 300, 103, 382, 87, 448, 100, 499, 99, 506, 176, 462, 193, 413, 240, 371, 227, 333, 239, 249, 249, 179, 306, 168, 328, 161, 375)

[node name="CollisionPolygon2D3" type="CollisionPolygon2D" parent="CrashObstacles"]
polygon = PackedVector2Array(-670, 82, -651, 3, -608, -29, -550, -72, -555, -97, -606, -144, -576, -172, -615, -153, -625, -238, -655, -240, -675, -292, -665, -349, -645, -431, -604, -484, -539, -523, -505, -554, -529, -615, -499, -664, -516, -735, -494, -809, -520, -872, -543, -927, -515, -978, -481, -1053, -493, -1128, -502, -1183, -607, -1185, -616, -1101, -636, -1046, -596, -984, -578, -926, -674, -955, -696, -940, -723, -862, -651, -854, -642, -799, -714, -775, -733, -723, -696, -688, -679, -610, -714, -549, -642, -540, -634, -484, -676, -440, -724, -427, -698, -350, -722, -314, -689, -278, -667, -226, -731, -216, -722, -183, -675, -132, -671, -82, -669, -58, -720, -23, -724, 11, -713, 53, -713, 79)

[node name="CollisionShape2D17" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-110.5, -307)
shape = SubResource("RectangleShape2D_0u4bd")

[node name="CollisionShape2D18" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(436, -305)
shape = SubResource("RectangleShape2D_0u4bd")

[node name="CollisionShape2D19" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(163, -320.5)
shape = SubResource("RectangleShape2D_erbwi")

[node name="CollisionShape2D20" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-866.5, 1327.5)
shape = SubResource("RectangleShape2D_s07sl")

[node name="CollisionPolygon2D4" type="CollisionPolygon2D" parent="CrashObstacles"]
polygon = PackedVector2Array(866, 983, 880, 934, 891, 865, 833, 871, 783, 837, 793, 783, 856, 777, 856, 722, 897, 687, 887, 635, 882, 570, 866, 509, 807, 453, 770, 416, 814, 369, 845, 319, 888, 300, 879, 259, 882, 199, 887, 150, 881, 83, 865, 21, 853, -37, 808, -74, 752, -103, 782, -141, 750, -175, 726, -222, 661, -265, 633, -300, 568, -322, 680, -348, 728, -355, 758, -391, 735, -435, 786, -467, 859, -466, 841, -510, 871, -559, 819, -602, 834, -654, 877, -687, 831, -733, 837, -821, 821, -913, 835, -1010, 820, -1061, 835, -1120, 865, -1161, 859, -1197, 866, -1199, 881, -1157, 868, -1090, 871, -1045, 875, -987, 883, -929, 903, -882, 883, -795, 897, -740, 892, -674, 887, -620, 903, -553, 879, -446, 772, -385, 803, -314, 789, -211, 801, -137, 880, -50, 900, 88, 901, 211, 905, 369, 894, 456, 886, 518, 903, 646, 908, 697, 916, 805, 915, 995)

[node name="CollisionPolygon2D5" type="CollisionPolygon2D" parent="CrashObstacles"]
polygon = PackedVector2Array(778, -1328, 719, -1361, 626, -1387, 555, -1385, 420, -1351, 391, -1295, 336, -1293, 323, -1214, 258, -1220, 226, -1315, 179, -1353, 118, -1375, 56, -1386, -8, -1445, -31, -1370, -109, -1375, -144, -1429, -197, -1417, -184, -1287, -215, -1219, -289, -1219, -338, -1289, -317, -1349, -394, -1372, -446, -1372, -496, -1433, -556, -1401, -607, -1348, -685, -1374, -722, -1317, -785, -1279, -789, -1207, -806, -1147, -826, -1090, -893, -1068, -910, -951, -924, -872, -992, -871, -992, -991, -993, -1498, 679, -1500, 761, -1396)

[node name="CollisionPolygon2D6" type="CollisionPolygon2D" parent="CrashObstacles"]
polygon = PackedVector2Array(-991, -867, -925, -868, -913, -760, -940, -693, -923, -554, -902, -499, -944, -447, -931, -349, -908, -247, -947, -192, -913, -65, -916, 7, -939, 69, -959, 109, -902, 141, -863, 187, -820, 221, -751, 239, -756, 262, -831, 231, -910, 240, -991, 278)

[node name="CollisionPolygon2D7" type="CollisionPolygon2D" parent="CrashObstacles"]
polygon = PackedVector2Array(-744, 245, -689, 250, -617, 229, -569, 245, -510, 191, -511, 103, -388, 96, -307, 125, -256, 94, -190, 135, -145, 196, -139, 232, -52, 249, -749, 265)

[node name="CollisionPolygon2D8" type="CollisionPolygon2D" parent="CrashObstacles"]
polygon = PackedVector2Array(713, -1499, 728, -1458, 778, -1434, 821, -1397, 822, -1281, 877, -1234, 931, -1195, 933, -464, 901, -418, 855, -410, 827, -373, 831, -140, 883, -96, 928, -63, 939, 1412, 885, 1465, 850, 1488, 917, 1489, 966, 1436, 955, -76, 914, -127, 851, -165, 859, -358, 946, -416, 966, -485, 955, -1229, 853, -1312, 851, -1439, 754, -1500)

[node name="CollisionPolygon2D9" type="CollisionPolygon2D" parent="CrashObstacles"]
polygon = PackedVector2Array(-301, 1288, -302, 1254, -239, 1169, -107, 1164, -77, 1128, -73, 1005, -37, 961, 164, 962, 185, 920, 187, 752, 219, 722, 341, 706, 348, 659, 347, 550, 409, 513, 450, 463, 491, 406, 521, 420, 529, 599, 662, 617, 688, 639, 573, 731, 580, 1218, 520, 1248, -34, 1248, -69, 1285)

[node name="CollisionPolygon2D10" type="CollisionPolygon2D" parent="CrashObstacles"]
polygon = PackedVector2Array(-940, 366, -901, 312, -86, 314, -84, 471, -132, 500, -195, 500, -244, 581, -363, 602, -370, 703, -488, 796, -742, 804, -733, 891, -645, 914, -653, 993, -729, 1043, -919, 1038, -939, 995)

[node name="CollisionPolygon2D11" type="CollisionPolygon2D" parent="CrashObstacles"]
polygon = PackedVector2Array(-47, 85, -14, 84, -33, 37, 25, -41, 68, -44, -2, -71, -71, -67, -134, -53, -87, -26, -27, -26, -56, 24)

[node name="CollisionShape2D26" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-983, 992)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D27" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-951, 1091)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D28" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-882, 1132)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D29" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-800, 1112)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D30" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-723, 1138)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D31" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-653, 1076)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D32" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-529, 976)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D33" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-603, 868)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D34" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-482, 893)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D35" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-384, 817)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D36" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-267, 772)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D37" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-288, 663)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D38" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-150, 565)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D39" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-53, 570)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D40" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(20, 410)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D41" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(325, 438)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D42" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(392, 382)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D43" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(111, 746)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D44" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(115, 859)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D45" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(45, 895)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D46" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-158, 1095)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D47" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-312, 1150)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D48" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-366, 1292)
shape = SubResource("CircleShape2D_5xlva")

[node name="CollisionShape2D49" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-314, -546)
shape = SubResource("CircleShape2D_tdodm")

[node name="CollisionShape2D50" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-209, -547)
shape = SubResource("CircleShape2D_tdodm")

[node name="CollisionShape2D51" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(266, -545)
shape = SubResource("CircleShape2D_tdodm")

[node name="CollisionShape2D52" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(366, -545)
shape = SubResource("CircleShape2D_tdodm")

[node name="CollisionShape2D53" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(-277, 940)
shape = SubResource("CircleShape2D_kl3yp")

[node name="CollisionShape2D54" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(690, 1293)
shape = SubResource("CircleShape2D_kl3yp")

[node name="BounceObstacles" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(-296, -204)
shape = SubResource("CapsuleShape2D_scayd")

[node name="CollisionShape2D2" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(-37, -160)
shape = SubResource("CapsuleShape2D_scayd")

[node name="CollisionShape2D3" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(216, -205)
shape = SubResource("CapsuleShape2D_scayd")

[node name="CollisionShape2D4" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(474, -159)
shape = SubResource("CapsuleShape2D_scayd")

[node name="CollisionShape2D5" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(33, -93)
rotation = 1.5708
shape = SubResource("CapsuleShape2D_erbwi")

[node name="CollisionShape2D6" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(-352, -309)
rotation = 1.5708
shape = SubResource("CapsuleShape2D_s07sl")

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="BounceObstacles"]
polygon = PackedVector2Array(-477, -704, -466, -704, -465, -688, -458, -668, -449, -646, -437, -626, -424, -605, -410, -589, -391, -575, -367, -563, -350, -554, -354, -546, -372, -551, -398, -564, -418, -581, -434, -600, -448, -621, -462, -643, -470, -664, -476, -687)

[node name="CollisionPolygon2D2" type="CollisionPolygon2D" parent="BounceObstacles"]
position = Vector2(577, 3)
polygon = PackedVector2Array(-477, -704, -466, -704, -465, -688, -458, -668, -449, -646, -437, -626, -424, -605, -410, -589, -391, -575, -367, -563, -350, -554, -354, -546, -372, -551, -398, -564, -418, -581, -434, -600, -448, -621, -462, -643, -470, -664, -476, -687)

[node name="CollisionPolygon2D3" type="CollisionPolygon2D" parent="BounceObstacles"]
polygon = PackedVector2Array(-176, -552, -171, -543, -153, -549, -129, -563, -108, -578, -89, -597, -77, -617, -64, -640, -55, -662, -49, -685, -36, -698, -10, -696, 7, -696, 27, -697, 45, -697, 65, -698, 80, -701, 80, -711, 64, -712, 45, -712, 26, -713, 6, -713, -12, -712, -39, -708, -56, -700, -64, -687, -68, -665, -75, -644, -87, -623, -101, -604, -116, -589, -135, -570, -158, -560)

[node name="CollisionPolygon2D4" type="CollisionPolygon2D" parent="BounceObstacles"]
position = Vector2(581, 9)
polygon = PackedVector2Array(-177, -558, -174, -550, -153, -549, -129, -563, -108, -578, -89, -597, -77, -617, -64, -640, -55, -662, -49, -685, -36, -698, -10, -696, 7, -696, 27, -697, 45, -697, 65, -698, 80, -701, 80, -711, 64, -712, 45, -712, 26, -713, 6, -713, -12, -712, -39, -708, -56, -700, -64, -687, -68, -665, -75, -644, -87, -623, -100, -608, -117, -591, -134, -576, -157, -564)

[node name="CollisionPolygon2D5" type="CollisionPolygon2D" parent="BounceObstacles"]
polygon = PackedVector2Array(-461, -1313, -442, -1307, -418, -1294, -400, -1282, -384, -1264, -372, -1243, -362, -1221, -353, -1201, -349, -1179, -342, -1167, -323, -1166, -296, -1160, -277, -1159, -259, -1160, -241, -1160, -221, -1159, -195, -1167, -178, -1168, -174, -1180, -167, -1203, -161, -1226, -149, -1247, -135, -1265, -119, -1283, -100, -1299, -77, -1311, -21, -1332, -13, -1330, 6, -1330, 26, -1330, 45, -1330, 62, -1329, 82, -1330, 154, -1297, 175, -1280, 187, -1264, 203, -1246, 214, -1224, 220, -1204, 224, -1181, 234, -1168, 278, -1161, 384, -1162, 396, -1166, 406, -1202, 413, -1223, 427, -1244, 453, -1280, 474, -1297, 496, -1307, 553, -1331, 661, -1330, 700, -1316, 734, -1301, 754, -1286, 769, -1267, 782, -1249, 794, -1226, 805, -1183, 810, -1166, 819, -1169, 803, -1224, 786, -1257, 757, -1293, 719, -1319, 655, -1338, 544, -1340, 506, -1320, 468, -1302, 428, -1271, 409, -1244, 387, -1177, 295, -1172, 241, -1178, 214, -1246, 178, -1291, 140, -1313, 82, -1339, -28, -1339, -100, -1309, -139, -1273, -173, -1225, -184, -1179, -210, -1168, -336, -1172, -353, -1228, -376, -1270, -419, -1304, -456, -1322, -462, -1314)

[node name="CollisionPolygon2D6" type="CollisionPolygon2D" parent="BounceObstacles"]
polygon = PackedVector2Array(-762, -1166, -752, -1165, -749, -1192, -742, -1213, -718, -1256, -703, -1276, -687, -1291, -662, -1307, -639, -1313, -641, -1321, -669, -1311, -693, -1295, -713, -1279, -727, -1260, -740, -1238, -751, -1213, -757, -1190, -762, -1167)

[node name="CollisionShape2D7" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(121, 510)
shape = SubResource("CircleShape2D_s07sl")

[node name="CollisionShape2D8" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(103, 524)
shape = SubResource("CircleShape2D_s07sl")

[node name="CollisionShape2D9" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(124, 582)
shape = SubResource("CircleShape2D_sc3sn")

[node name="CollisionShape2D10" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(-102, 738)
shape = SubResource("CircleShape2D_sc3sn")

[node name="CollisionShape2D11" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(-79, 757)
shape = SubResource("CircleShape2D_mta1t")

[node name="CollisionShape2D12" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(-39, 714)
shape = SubResource("CircleShape2D_mta1t")

[node name="CollisionShape2D13" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(-328, 972)
shape = SubResource("CircleShape2D_mta1t")

[node name="CollisionShape2D14" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(-320, 1004)
shape = SubResource("CircleShape2D_cjelv")

[node name="CollisionShape2D15" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(-458, 1164)
shape = SubResource("CircleShape2D_ngti7")

[node name="CollisionShape2D16" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(-522, 1214)
shape = SubResource("CircleShape2D_wiju8")

[node name="StartPosition" type="Marker2D" parent="."]
position = Vector2(-31, 1379)

[node name="StartLap" type="Area2D" parent="."]
position = Vector2(25, 1398)
rotation = 1.5708
scale = Vector2(4.8, 0.8)
collision_layer = 2
collision_mask = 2
script = ExtResource("14_mta1t")

[node name="CollisionShape2D" type="CollisionShape2D" parent="StartLap"]
shape = SubResource("RectangleShape2D_4lbuf")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="Label" type="Label" parent="CanvasLayer" node_paths=PackedStringArray("start_lap_interaction", "racer")]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -1238.0
offset_top = 167.0
offset_right = -1088.0
offset_bottom = 267.0
grow_horizontal = 0
label_settings = SubResource("LabelSettings_0u4bd")
script = ExtResource("12_mta1t")
start_lap_interaction = NodePath("../../StartLap")
racer = NodePath("../../Racecar")

[node name="SubViewportContainer" type="SubViewportContainer" parent="CanvasLayer"]
visible = false
offset_left = 42.0
offset_top = 31.0
offset_right = 554.0
offset_bottom = 543.0
size_flags_horizontal = 8

[node name="SubViewport" type="SubViewport" parent="CanvasLayer/SubViewportContainer" node_paths=PackedStringArray("Racecar")]
handle_input_locally = false
size = Vector2i(128, 128)
render_target_update_mode = 0
script = ExtResource("6_jy1fd")
Racecar = NodePath("../../../Racecar")

[node name="Camera2D" type="Camera2D" parent="CanvasLayer/SubViewportContainer/SubViewport"]
zoom = Vector2(0.1, 0.1)
limit_left = -1000
limit_top = -1500
limit_right = 1000
limit_bottom = 1500
position_smoothing_enabled = true

[node name="TrackArea" type="Area2D" parent="."]
collision_layer = 4
collision_mask = 4
script = ExtResource("14_mta1t")

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="TrackArea"]
polygon = PackedVector2Array(-682, 1351, -682, 1445, 689.359, 1445, 694, 1445, 692, 1350)

[node name="CollisionPolygon2D2" type="CollisionPolygon2D" parent="TrackArea"]
polygon = PackedVector2Array(690, 1350, 717, 1346, 751, 1318, 763, 1281, 762, 1133, 760, 983, 654, 876, 636, 827, 635, 781, 655, 732, 686, 696, 761, 621, 761, 596, 717, 551, 659, 492, 642, 463, 635, 414, 641, 372, 663, 333, 761, 234, 762, 82, 650, -34, 636, -76, 632, -117, 611, -132, 611, -230, 639, -226, 690, -199, 722, -155, 732, -124, 738, -83, 844, 24, 858, 68, 860, 240, 849, 279, 828, 306, 733, 402, 738, 435, 832, 527, 852, 559, 859, 586, 859, 641, 836, 688, 737, 784, 732, 805, 741, 826, 844, 928, 857, 972, 859, 1294, 842, 1346, 813, 1390, 761, 1432, 693, 1446)

[node name="CollisionPolygon2D3" type="CollisionPolygon2D" parent="TrackArea"]
polygon = PackedVector2Array(613, -229, -452, -230, -491, -246, -516, -294, -514, -363, -498, -396, -450, -422, 634, -421, 692, -433, 746, -471, 778, -517, 798, -570, 796, -594, 699, -595, 694, -561, 672, -533, 635, -518, -455, -517, -509, -503, -557, -471, -586, -434, -610, -384, -612, -345, -611, -277, -591, -222, -554, -174, -491, -139, -446, -132, 612, -132)

[node name="CollisionPolygon2D4" type="CollisionPolygon2D" parent="TrackArea"]
polygon = PackedVector2Array(700, -593, 699, -1157, 682, -1195, 636, -1221, 573, -1221, 525, -1196, 508, -1156, 509, -701, 490, -644, 455, -596, 398, -558, 342, -548, 273, -550, 223, -563, 163, -609, 133, -666, 123, -711, 124, -1157, 103, -1199, 59, -1220, -4, -1220, -40, -1206, -61, -1179, -68, -1153, -68, -706, -76, -667, -101, -621, -135, -585, -184, -559, -233, -549, -295, -549, -346, -562, -402, -599, -433, -642, -453, -700, -452, -1159, -472, -1202, -516, -1221, -580, -1220, -624, -1200, -644, -1161, -644, -1094, -668, -1046, -767, -951, -772, -938, -868, -937, -863, -979, -832, -1024, -740, -1118, -737, -1181, -701, -1256, -643, -1300, -595, -1319, -509, -1319, -442, -1294, -394, -1256, -362, -1194, -356, -1153, -355, -706, -340, -673, -311, -651, -228, -645, -181, -670, -164, -709, -164, -1154, -155, -1202, -119, -1261, -66, -1302, -9, -1319, 67, -1319, 135, -1295, 192, -1242, 220, -1169, 220, -709, 235, -670, 284, -645, 357, -647, 395, -672, 412, -710, 410, -1155, 424, -1212, 467, -1271, 538, -1314, 649, -1317, 708, -1298, 767, -1243, 796, -1173, 797, -589)

[node name="CollisionPolygon2D5" type="CollisionPolygon2D" parent="TrackArea"]
polygon = PackedVector2Array(-868, -939, -868, 67, -862, 92, -855, 114, -840, 143, -824, 162, -793, 191, -753, 212, -704, 222, -652, 222, -622, 211, -587, 188, -565, 158, -548, 117, -547, 87, -538, 70, -519, 61, -197, 60, -172, 72, -164, 89, -164, 115, -157, 138, -138, 172, -98, 208, -55, 220, -10, 220, 33, 202, 65, 172, 86, 136, 93, 86, 103, 67, 128, 59, 483, 62, 513, 80, 532, 106, 536, 128, 535, 194, 519, 227, 485, 247, 473, 250, 299, 251, 243, 269, 208, 304, 186, 348, 186, 395, 186, 492, 282, 492, 283, 374, 291, 358, 315, 347, 478, 348, 527, 333, 568, 309, 604, 271, 624, 229, 635, 179, 630, 96, 605, 39, 561, -5, 510, -31, 469, -38, 94, -37, 43, -12, 7, 30, -5, 70, -5, 99, -24, 120, -48, 120, -68, 96, -71, 53, -99, 5, -143, -26, -174, -37, -477, -36, -537, -37, -588, -17, -630, 26, -645, 72, -650, 108, -669, 123, -718, 123, -756, 97, -773, 56, -772, -940)

[node name="CollisionPolygon2D6" type="CollisionPolygon2D" parent="TrackArea"]
polygon = PackedVector2Array(-23, 702, -22, 600, -20, 531, 8, 472, 63, 420, 125, 398, 188, 397, 187, 493, 124, 495, 91, 514, 74, 552, 75, 601, 120, 603, 153, 593, 179, 564, 186, 526, 189, 486, 280, 484, 282, 554, 259, 615, 213, 667, 152, 696, 73, 700)

[node name="CollisionPolygon2D7" type="CollisionPolygon2D" parent="TrackArea"]
position = Vector2(-208, 207)
polygon = PackedVector2Array(-23, 702, -22, 600, -20, 531, 8, 472, 63, 420, 125, 398, 188, 397, 187, 493, 124, 495, 91, 514, 74, 552, 75, 601, 120, 603, 153, 593, 179, 564, 186, 526, 189, 486, 280, 484, 282, 554, 259, 615, 213, 667, 152, 696, 73, 700)

[node name="CollisionPolygon2D8" type="CollisionPolygon2D" parent="TrackArea"]
position = Vector2(-417, 417)
polygon = PackedVector2Array(-23, 702, -22, 600, -20, 531, 8, 472, 63, 420, 125, 398, 188, 397, 187, 493, 124, 495, 91, 514, 74, 552, 75, 601, 120, 603, 153, 593, 179, 564, 186, 526, 189, 486, 280, 484, 282, 554, 259, 615, 213, 667, 152, 696, 73, 700)

[node name="CollisionPolygon2D9" type="CollisionPolygon2D" parent="TrackArea"]
position = Vector2(-625, 625)
polygon = PackedVector2Array(-23, 702, -22, 600, -20, 531, 8, 472, 63, 420, 125, 398, 188, 397, 187, 493, 124, 495, 91, 514, 74, 552, 75, 601, 120, 603, 153, 593, 179, 564, 186, 526, 189, 486, 280, 484, 282, 554, 259, 615, 213, 667, 152, 696, 73, 700)

[node name="CollisionPolygon2D10" type="CollisionPolygon2D" parent="TrackArea"]
polygon = PackedVector2Array(-644, 1354, -645, 1320, -549, 1323, -550, 1355)

[node name="SlowArea" type="Area2D" parent="."]
collision_layer = 4
collision_mask = 4
script = ExtResource("14_mta1t")

[node name="CollisionShape2D" type="CollisionShape2D" parent="SlowArea"]
position = Vector2(-260, -891.75)
shape = SubResource("RectangleShape2D_sc3sn")

[node name="CollisionShape2D2" type="CollisionShape2D" parent="SlowArea"]
position = Vector2(27, -965)
shape = SubResource("RectangleShape2D_sc3sn")

[node name="CollisionShape2D3" type="CollisionShape2D" parent="SlowArea"]
position = Vector2(317, -904)
shape = SubResource("RectangleShape2D_sc3sn")

[node name="CollisionShape2D4" type="CollisionShape2D" parent="SlowArea"]
position = Vector2(607, -961)
shape = SubResource("RectangleShape2D_sc3sn")

[node name="CollisionShape2D5" type="CollisionShape2D" parent="SlowArea"]
position = Vector2(72, -526.5)
shape = SubResource("RectangleShape2D_mta1t")

[node name="CollisionShape2D6" type="CollisionShape2D" parent="SlowArea"]
position = Vector2(72, -411.5)
shape = SubResource("RectangleShape2D_mta1t")

[node name="CollisionShape2D7" type="CollisionShape2D" parent="SlowArea"]
position = Vector2(-877, -438.5)
shape = SubResource("RectangleShape2D_cjelv")

[node name="CollisionShape2D8" type="CollisionShape2D" parent="SlowArea"]
position = Vector2(-763, -438.5)
shape = SubResource("RectangleShape2D_cjelv")

[node name="CollisionShape2D9" type="CollisionShape2D" parent="SlowArea"]
position = Vector2(-109.5, -305.5)
shape = SubResource("RectangleShape2D_ngti7")

[node name="CollisionShape2D10" type="CollisionShape2D" parent="SlowArea"]
position = Vector2(435.5, -305.5)
shape = SubResource("RectangleShape2D_ngti7")

[node name="StillObstacles" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StillObstacles"]
position = Vector2(-352, -55)
shape = SubResource("RectangleShape2D_wiju8")

[node name="CollisionShape2D2" type="CollisionShape2D" parent="StillObstacles"]
position = Vector2(339, -58)
shape = SubResource("RectangleShape2D_5xlva")

[node name="CollisionShape2D3" type="CollisionShape2D" parent="StillObstacles"]
position = Vector2(42.5, 1339)
shape = SubResource("RectangleShape2D_tdodm")

[node name="CollisionShape2D4" type="CollisionShape2D" parent="StillObstacles"]
position = Vector2(-23, 1458)
shape = SubResource("RectangleShape2D_kl3yp")
